"""
A.T.L.A.S Models - Clean Implementation
This file provides all required data models for the Atlas trading system.
"""

import sys
import os
from datetime import datetime
from typing import Optional, Dict, List, Any
from dataclasses import dataclass
from enum import Enum

# Add consolidated path to imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'atlas_v5_consolidated', 'core'))

# Try to import consolidated models, but always define fallbacks
CONSOLIDATED_MODELS_AVAILABLE = False
try:
    from models import *
    CONSOLIDATED_MODELS_AVAILABLE = True
except ImportError:
    pass

# Define all required models
class EngineStatus(Enum):
    """Engine status enumeration"""
    INITIALIZING = "initializing"
    ACTIVE = "active"
    STOPPED = "stopped"
    FAILED = "failed"
    MAINTENANCE = "maintenance"

class SignalStrength(Enum):
    """Signal strength enumeration"""
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"
    VERY_STRONG = "very_strong"

class AlertType(Enum):
    """Alert type enumeration"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class AlertPriority(Enum):
    """Alert priority enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class OrderSide(Enum):
    """Order side enumeration"""
    BUY = "buy"
    SELL = "sell"

class OrderType(Enum):
    """Order type enumeration"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    BRACKET = "bracket"
    TRAILING_STOP = "trailing_stop"

class OrderStatus(Enum):
    """Order status enumeration"""
    NEW = "new"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELED = "canceled"
    REJECTED = "rejected"

class CommunicationMode(Enum):
    """Communication mode enumeration"""
    CHAT = "chat"
    VOICE = "voice"
    EMAIL = "email"
    SMS = "sms"
    NOTIFICATION = "notification"

@dataclass
class Quote:
    """Stock quote data structure"""
    symbol: str
    price: float
    change: float
    change_percent: float
    volume: int
    timestamp: datetime
    bid: Optional[float] = None
    ask: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None
    open: Optional[float] = None
    previous_close: Optional[float] = None

@dataclass
class Order:
    """Trading order data structure"""
    symbol: str
    quantity: float
    side: OrderSide
    type: OrderType
    timestamp: datetime
    id: Optional[str] = None
    price: Optional[float] = None
    stop_price: Optional[float] = None
    status: str = "NEW"
    filled_quantity: float = 0.0

@dataclass
class Position:
    """Trading position data structure"""
    symbol: str
    quantity: float
    average_price: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float
    timestamp: datetime

@dataclass
class Trade:
    """Trade execution data structure"""
    symbol: str
    action: str
    quantity: float
    price: float
    timestamp: datetime
    trade_id: str
    status: str
    commission: float = 0.0

@dataclass
class TTMSqueezeSignal:
    """TTM Squeeze signal data structure"""
    symbol: str
    signal_type: str
    strength: SignalStrength
    confidence: float
    price: float
    timestamp: datetime
    criteria_met: List[str]
    technical_indicators: Dict[str, float]

@dataclass
class LeeMethodSignal:
    """Lee Method signal data structure"""
    symbol: str
    pattern_type: str
    confidence: float
    entry_price: float
    target_price: float
    stop_loss: float
    timestamp: datetime
    criteria_scores: Dict[str, float]

@dataclass
class TradingSignal:
    """General trading signal data structure"""
    symbol: str
    signal_type: str
    action: str  # BUY, SELL, HOLD
    confidence: float
    price: float
    timestamp: datetime
    source: str
    metadata: Dict[str, Any]

@dataclass
class PortfolioSummary:
    """Portfolio summary data structure"""
    total_value: float
    cash: float
    buying_power: float
    day_pnl: float
    total_pnl: float
    positions_count: int
    timestamp: datetime

@dataclass
class MarketData:
    """Market data container"""
    symbol: str
    quotes: List[Any]
    historical_data: Optional[Dict[str, Any]] = None
    real_time_data: Optional[Dict[str, Any]] = None

@dataclass
class ScanResult:
    """Scanner result data structure"""
    symbol: str
    pattern_found: bool
    pattern_type: str
    confidence: float
    signal_strength: SignalStrength
    price: float
    volume: int
    timestamp: datetime

@dataclass
class AlertSignal:
    """Alert signal data structure"""
    alert_id: str
    symbol: str
    alert_type: AlertType
    priority: AlertPriority
    message: str
    timestamp: datetime
    metadata: Dict[str, Any]

@dataclass
class RiskMetrics:
    """Risk metrics data structure"""
    symbol: str
    var_95: float  # Value at Risk 95%
    volatility: float
    beta: float
    sharpe_ratio: float
    max_drawdown: float
    timestamp: datetime

@dataclass
class PerformanceMetrics:
    """Performance metrics data structure"""
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    timestamp: datetime

@dataclass
class MLPredictionResult:
    """Machine learning prediction result"""
    symbol: str
    prediction_type: str
    predicted_value: float
    confidence: float
    model_used: str
    features_used: List[str]
    timestamp: datetime

@dataclass
class NewsItem:
    """News item data structure"""
    title: str
    content: str
    source: str
    url: str
    timestamp: datetime
    sentiment_score: Optional[float] = None
    relevance_score: Optional[float] = None

@dataclass
class SentimentData:
    """Sentiment analysis data structure"""
    symbol: str
    sentiment_score: float
    sentiment_label: str  # POSITIVE, NEGATIVE, NEUTRAL
    confidence: float
    source: str
    timestamp: datetime

@dataclass
class TradingPlanTarget:
    """Trading plan target data structure"""
    price: float
    probability: float
    timeframe: str
    reasoning: str

@dataclass
class TradingOpportunity:
    """Trading opportunity data structure"""
    symbol: str
    opportunity_type: str
    confidence: float
    entry_price: float
    target_price: float
    stop_loss: float
    risk_reward_ratio: float
    timestamp: datetime
    reasoning: str

@dataclass
class OptionsChain:
    """Options chain data structure"""
    symbol: str
    expiration_date: datetime
    strike_price: float
    call_price: float
    put_price: float
    call_volume: int
    put_volume: int
    timestamp: datetime

@dataclass
class MarketSentiment:
    """Market sentiment data structure"""
    symbol: str
    sentiment_score: float
    sentiment_label: str
    confidence: float
    sources: List[str]
    timestamp: datetime

@dataclass
class TradingPlanAlert:
    """Trading plan alert data structure"""
    alert_id: str
    plan_id: str
    symbol: str
    alert_type: str
    message: str
    priority: AlertPriority
    timestamp: datetime
    triggered_price: float
    action_required: str

@dataclass
class TradingPlanExecution:
    """Trading plan execution data structure"""
    execution_id: str
    plan_id: str
    symbol: str
    action: str
    quantity: float
    price: float
    timestamp: datetime
    status: str
    commission: float = 0.0
    notes: Optional[str] = None

@dataclass
class AIResponse:
    """Enhanced AI response data structure for Atlas V4 Enhanced"""
    # Core response data
    response: str
    confidence: float

    # Response classification
    type: Optional[str] = None  # Response type (greeting, stock_analysis, etc.)
    response_type: Optional[str] = None  # Alternative name for type

    # Session and context
    session_id: Optional[str] = None
    timestamp: Optional[datetime] = None

    # Enhanced features
    content: Optional[str] = None  # Alternative name for response
    metadata: Optional[Dict[str, Any]] = None

    # Legacy compatibility
    response_id: Optional[str] = None
    query: Optional[str] = None
    model_used: Optional[str] = None

    def __post_init__(self):
        """Post-initialization to handle parameter aliases and defaults"""
        # Handle timestamp default
        if self.timestamp is None:
            self.timestamp = datetime.now()

        # Handle content/response aliases
        if self.content is None and self.response:
            self.content = self.response
        elif self.response is None and self.content:
            self.response = self.content

        # Handle type/response_type aliases
        if self.response_type is None and self.type:
            self.response_type = self.type
        elif self.type is None and self.response_type:
            self.type = self.response_type

        # Initialize metadata if None
        if self.metadata is None:
            self.metadata = {}

        # Generate response_id if not provided
        if self.response_id is None:
            import uuid
            self.response_id = str(uuid.uuid4())[:8]

@dataclass
class EmotionalState:
    """Emotional state data structure"""
    state_id: str
    emotion: str
    intensity: float
    confidence: float
    timestamp: datetime
    context: str

@dataclass
class TechnicalIndicators:
    """Technical indicators data structure"""
    symbol: str
    rsi: Optional[float] = None
    macd: Optional[float] = None
    bollinger_upper: Optional[float] = None
    bollinger_lower: Optional[float] = None
    sma_20: Optional[float] = None
    sma_50: Optional[float] = None
    volume_avg: Optional[float] = None
    timestamp: datetime = None

@dataclass
class RiskAssessment:
    """Risk assessment data structure"""
    assessment_id: str
    symbol: str
    risk_level: str
    risk_score: float
    factors: List[str]
    recommendations: List[str]
    timestamp: datetime

@dataclass
class TradingGoal:
    """Trading goal data structure"""
    goal_id: str
    description: str
    target_value: float
    current_value: float
    deadline: datetime
    priority: str
    status: str
    created_at: datetime

@dataclass
class ContextMemory:
    """Context memory data structure"""
    memory_id: str
    context: str
    importance: float
    timestamp: datetime
    metadata: Dict[str, Any]

@dataclass
class ConversationContext:
    """Conversation context data structure"""
    context_id: str
    user_id: str
    conversation_history: List[Dict[str, Any]]
    current_topic: str
    preferences: Dict[str, Any]
    timestamp: datetime

@dataclass
class UserProfile:
    """User profile data structure"""
    user_id: str
    username: str
    preferences: Dict[str, Any]
    trading_experience: str
    risk_tolerance: str
    created_at: datetime
    last_active: datetime

@dataclass
class SystemMetrics:
    """System metrics data structure"""
    metric_id: str
    metric_name: str
    value: float
    unit: str
    timestamp: datetime
    category: str

@dataclass
class PredictoForecast:
    """Predicto forecast data structure"""
    forecast_id: str
    symbol: str
    prediction: float
    confidence: float
    timeframe: str
    timestamp: datetime
    model_version: str

@dataclass
class PortfolioIntegration:
    """Portfolio integration data structure"""
    integration_id: str
    portfolio_id: str
    platform: str
    status: str
    last_sync: datetime
    configuration: Dict[str, Any]

@dataclass
class TradingPlanScenario:
    """Trading plan scenario data structure"""
    scenario_id: str
    plan_id: str
    name: str
    description: str
    probability: float
    expected_return: float
    risk_level: str
    actions: List[Dict[str, Any]]

@dataclass
class TradingPlanMonitoring:
    """Trading plan monitoring data structure"""
    monitoring_id: str
    plan_id: str
    status: str
    progress: float
    alerts: List[Dict[str, Any]]
    last_updated: datetime
    metrics: Dict[str, Any]

@dataclass
class ComprehensiveTradingPlan:
    """Comprehensive trading plan data structure"""
    symbol: str
    plan_id: str
    strategy: str
    entry_price: float
    target_price: float
    stop_loss: float
    position_size: float
    risk_amount: float
    confidence: float
    timeframe: str
    created_at: datetime
    status: str = "PENDING"
    notes: Optional[str] = None
    targets: List[TradingPlanTarget] = None

class SystemHealth:
    """System health monitoring"""
    def __init__(self):
        self.status = EngineStatus.ACTIVE
        self.components = {}
        self.last_check = datetime.now()
        
    def update_component(self, component: str, status: EngineStatus):
        self.components[component] = {
            "status": status,
            "last_update": datetime.now()
        }
        
    def get_overall_status(self) -> EngineStatus:
        if not self.components:
            return EngineStatus.INITIALIZING
            
        statuses = [comp["status"] for comp in self.components.values()]
        
        if any(status == EngineStatus.FAILED for status in statuses):
            return EngineStatus.FAILED
        elif any(status == EngineStatus.MAINTENANCE for status in statuses):
            return EngineStatus.MAINTENANCE
        elif all(status == EngineStatus.ACTIVE for status in statuses):
            return EngineStatus.ACTIVE
        else:
            return EngineStatus.INITIALIZING

# Export all models for compatibility
__all__ = [
    'EngineStatus', 'SignalStrength', 'AlertType', 'AlertPriority',
    'OrderSide', 'OrderType', 'OrderStatus', 'CommunicationMode',
    'Quote', 'Order', 'Position', 'Trade',
    'TTMSqueezeSignal', 'LeeMethodSignal', 'TradingSignal',
    'PortfolioSummary', 'MarketData', 'ScanResult',
    'AlertSignal', 'RiskMetrics', 'PerformanceMetrics', 'MLPredictionResult',
    'NewsItem', 'SentimentData', 'SystemHealth', 'ComprehensiveTradingPlan',
    'TradingPlanTarget', 'TradingOpportunity', 'OptionsChain', 'MarketSentiment',
    'TradingPlanAlert', 'TradingPlanExecution', 'AIResponse', 'EmotionalState',
    'TechnicalIndicators', 'RiskAssessment', 'TradingGoal', 'ContextMemory',
    'ConversationContext', 'UserProfile', 'SystemMetrics', 'PredictoForecast',
    'PortfolioIntegration', 'TradingPlanScenario', 'TradingPlanMonitoring'
]
